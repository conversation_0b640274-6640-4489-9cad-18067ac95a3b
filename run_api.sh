#!/bin/bash
# Trading Analysis API Runner

echo "🚀 Starting Trading Analysis API"
echo "================================"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Virtual environment not found. Creating one..."
    python3 setup_env.py
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Check if API_KEY is set
if [ -z "$API_KEY" ]; then
    echo "⚠️  API_KEY not set. Using default for testing..."
    export API_KEY="test-api-key-12345"
    echo "🔑 API Key set to: $API_KEY"
fi

# Start the API
echo "🌐 Starting Flask API on http://localhost:5000"
echo "📖 Documentation: http://localhost:5000/docs"
echo "❤️  Health check: http://localhost:5000/health"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

python Trade.py