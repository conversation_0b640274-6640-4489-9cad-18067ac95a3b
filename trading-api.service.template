[Unit]
Description=Trading Analysis API
Documentation=https://github.com/your-username/automating-technical-analysis
After=network.target network-online.target
Wants=network-online.target
StartLimitIntervalSec=500
StartLimitBurst=5

[Service]
Type=simple
User=REPLACE_WITH_YOUR_USERNAME
Group=REPLACE_WITH_YOUR_USERNAME
WorkingDirectory=REPLACE_WITH_FULL_PATH_TO_PROJECT
Environment=API_KEY=REPLACE_WITH_YOUR_API_KEY
Environment=PATH=REPLACE_WITH_FULL_PATH_TO_PROJECT/venv/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=REPLACE_WITH_FULL_PATH_TO_PROJECT/venv/bin/python Trade.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=REPLACE_WITH_FULL_PATH_TO_PROJECT

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=trading-api

# Resource limits
LimitNOFILE=65536
MemoryMax=2G

[Install]
WantedBy=multi-user.target

# Installation Instructions:
# 1. Copy this file to /etc/systemd/system/trading-api.service
# 2. Replace all REPLACE_WITH_* placeholders with actual values
# 3. Run: sudo systemctl daemon-reload
# 4. Run: sudo systemctl enable trading-api
# 5. Run: sudo systemctl start trading-api
#
# Example replacements:
# REPLACE_WITH_YOUR_USERNAME -> your-username
# REPLACE_WITH_FULL_PATH_TO_PROJECT -> /home/<USER>/automating-technical-analysis
# REPLACE_WITH_YOUR_API_KEY -> your-secret-api-key-here
#
# Commands:
# sudo systemctl status trading-api    # Check status
# sudo systemctl start trading-api     # Start service
# sudo systemctl stop trading-api      # Stop service
# sudo systemctl restart trading-api   # Restart service
# sudo journalctl -u trading-api -f    # View logs
