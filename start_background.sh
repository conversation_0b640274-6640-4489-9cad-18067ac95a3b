#!/bin/bash

# Trading Analysis API - Background Startup Script
# This script starts the API in the background and provides management options

API_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$API_DIR/trading-api.pid"
LOG_FILE="$API_DIR/trading-api.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if API is running
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# Function to get API status
get_status() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        print_success "API is running (PID: $pid)"
        
        # Check if API is responding
        if curl -s --max-time 5 http://localhost:5000/health > /dev/null 2>&1; then
            print_success "API is responding on http://localhost:5000"
        else
            print_warning "API process is running but not responding"
        fi
    else
        print_error "API is not running"
    fi
}

# Function to start API
start_api() {
    if is_running; then
        print_warning "API is already running"
        get_status
        return 1
    fi
    
    print_status "Starting Trading Analysis API..."
    
    cd "$API_DIR"
    
    # Start the API in background
    nohup ./run_api.sh > "$LOG_FILE" 2>&1 &
    local pid=$!
    
    # Save PID
    echo $pid > "$PID_FILE"
    
    # Wait a moment and check if it started successfully
    sleep 5
    
    if is_running; then
        print_success "API started successfully (PID: $pid)"
        print_status "Logs: $LOG_FILE"
        print_status "Health check: http://localhost:5000/health"
        
        # Wait for API to be ready
        print_status "Waiting for API to be ready..."
        local attempts=0
        while [ $attempts -lt 30 ]; do
            if curl -s --max-time 2 http://localhost:5000/health > /dev/null 2>&1; then
                print_success "API is ready and responding!"
                return 0
            fi
            sleep 2
            attempts=$((attempts + 1))
            echo -n "."
        done
        echo
        print_warning "API started but may still be initializing. Check logs: tail -f $LOG_FILE"
    else
        print_error "Failed to start API. Check logs: $LOG_FILE"
        return 1
    fi
}

# Function to stop API
stop_api() {
    if ! is_running; then
        print_warning "API is not running"
        return 1
    fi
    
    local pid=$(cat "$PID_FILE")
    print_status "Stopping Trading Analysis API (PID: $pid)..."
    
    # Try graceful shutdown first
    kill "$pid" 2>/dev/null
    
    # Wait for graceful shutdown
    local attempts=0
    while [ $attempts -lt 10 ]; do
        if ! ps -p "$pid" > /dev/null 2>&1; then
            break
        fi
        sleep 1
        attempts=$((attempts + 1))
    done
    
    # Force kill if still running
    if ps -p "$pid" > /dev/null 2>&1; then
        print_warning "Forcing shutdown..."
        kill -9 "$pid" 2>/dev/null
    fi
    
    rm -f "$PID_FILE"
    print_success "API stopped"
}

# Function to restart API
restart_api() {
    print_status "Restarting Trading Analysis API..."
    stop_api
    sleep 2
    start_api
}

# Function to show logs
show_logs() {
    if [ -f "$LOG_FILE" ]; then
        echo -e "${BLUE}=== Trading API Logs ===${NC}"
        tail -f "$LOG_FILE"
    else
        print_error "Log file not found: $LOG_FILE"
    fi
}

# Function to test API
test_api() {
    print_status "Testing Trading Analysis API..."
    
    # Test health endpoint
    if curl -s --max-time 5 http://localhost:5000/health > /dev/null; then
        print_success "Health check passed"
        echo "Response: $(curl -s http://localhost:5000/health)"
    else
        print_error "Health check failed"
        return 1
    fi
    
    # Test data sources
    print_status "Testing data sources..."
    local test_response=$(curl -s --max-time 10 -H "X-API-Key: test-api-key-12345" http://localhost:5000/test)
    if [ $? -eq 0 ]; then
        print_success "Data source test completed"
        echo "$test_response" | python3 -m json.tool 2>/dev/null || echo "$test_response"
    else
        print_error "Data source test failed"
    fi
}

# Function to show help
show_help() {
    echo -e "${BLUE}Trading Analysis API - Background Management${NC}"
    echo
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  start     Start the API in background"
    echo "  stop      Stop the API"
    echo "  restart   Restart the API"
    echo "  status    Show API status"
    echo "  logs      Show real-time logs"
    echo "  test      Test API functionality"
    echo "  help      Show this help message"
    echo
    echo "Examples:"
    echo "  $0 start          # Start API in background"
    echo "  $0 status         # Check if API is running"
    echo "  $0 logs           # View real-time logs"
    echo "  $0 test           # Test API endpoints"
    echo
    echo "Files:"
    echo "  PID file: $PID_FILE"
    echo "  Log file: $LOG_FILE"
    echo "  API URL:  http://localhost:5000"
}

# Main script logic
case "${1:-help}" in
    start)
        start_api
        ;;
    stop)
        stop_api
        ;;
    restart)
        restart_api
        ;;
    status)
        get_status
        ;;
    logs)
        show_logs
        ;;
    test)
        test_api
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo
        show_help
        exit 1
        ;;
esac
