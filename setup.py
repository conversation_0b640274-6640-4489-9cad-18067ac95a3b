from setuptools import setup, find_packages

setup(
    name="trading-analysis-api",
    version="1.0.0",
    description="Flask-based trading analysis API with ML predictions",
    author="Your Name",
    author_email="<EMAIL>",
    packages=find_packages(),
    python_requires=">=3.8,<3.12",
    install_requires=[
        "Flask>=2.3.0,<3.0.0",
        "h5py>=3.8.0,<4.0.0",
        "html5lib>=1.1,<2.0",
        "Keras>=2.13.0,<3.0.0",
        "numpy>=1.24.0,<2.0.0",
        "pandas>=2.0.0,<3.0.0",
        "plotly>=5.15.0,<6.0.0",
        "requests>=2.31.0,<3.0.0",
        "scikit-learn>=1.3.0,<2.0.0",
        "tensorflow>=2.13.0,<3.0.0",
        "typing_extensions>=4.7.0,<5.0.0",
        "yfinance>=0.2.18,<1.0.0",
        "Werkzeug>=2.3.0,<3.0.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
        ]
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
)