#!/bin/bash

# Trading Analysis API - Systemd Service Installation Script
# This script helps install the API as a systemd service for auto-start on boot

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get current directory and user
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CURRENT_USER="$(whoami)"
SERVICE_NAME="trading-api"
SERVICE_FILE="/etc/systemd/system/${SERVICE_NAME}.service"
TEMPLATE_FILE="$SCRIPT_DIR/trading-api.service.template"

print_status "Trading Analysis API - Systemd Service Installer"
echo "=================================================="
echo

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_error "Please do not run this script as root. Run as your regular user."
    print_status "The script will use sudo when needed."
    exit 1
fi

# Check if template exists
if [ ! -f "$TEMPLATE_FILE" ]; then
    print_error "Template file not found: $TEMPLATE_FILE"
    exit 1
fi

# Get user input
echo "Current settings:"
echo "  User: $CURRENT_USER"
echo "  Project directory: $SCRIPT_DIR"
echo

read -p "Enter your API key (or press Enter for default test key): " API_KEY
if [ -z "$API_KEY" ]; then
    API_KEY="test-api-key-12345"
    print_warning "Using default test API key. Change this in production!"
fi

echo
print_status "Configuration:"
echo "  User: $CURRENT_USER"
echo "  Project directory: $SCRIPT_DIR"
echo "  API key: ${API_KEY:0:10}..."
echo "  Service file: $SERVICE_FILE"
echo

read -p "Continue with installation? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_status "Installation cancelled."
    exit 0
fi

# Check if virtual environment exists
VENV_PATH="$SCRIPT_DIR/venv"
if [ ! -d "$VENV_PATH" ]; then
    print_warning "Virtual environment not found at $VENV_PATH"
    print_status "Creating virtual environment..."
    
    python3 -m venv "$VENV_PATH"
    if [ $? -ne 0 ]; then
        print_error "Failed to create virtual environment"
        exit 1
    fi
    
    print_status "Installing dependencies..."
    "$VENV_PATH/bin/pip" install -r "$SCRIPT_DIR/requirements.txt"
    if [ $? -ne 0 ]; then
        print_error "Failed to install dependencies"
        exit 1
    fi
fi

# Create service file from template
print_status "Creating systemd service file..."

# Create temporary service file
TEMP_SERVICE="/tmp/${SERVICE_NAME}.service"
cp "$TEMPLATE_FILE" "$TEMP_SERVICE"

# Replace placeholders
sed -i "s|REPLACE_WITH_YOUR_USERNAME|$CURRENT_USER|g" "$TEMP_SERVICE"
sed -i "s|REPLACE_WITH_FULL_PATH_TO_PROJECT|$SCRIPT_DIR|g" "$TEMP_SERVICE"
sed -i "s|REPLACE_WITH_YOUR_API_KEY|$API_KEY|g" "$TEMP_SERVICE"

# Remove comments and empty lines for cleaner service file
grep -v '^#' "$TEMP_SERVICE" | grep -v '^$' > "${TEMP_SERVICE}.clean"
mv "${TEMP_SERVICE}.clean" "$TEMP_SERVICE"

# Install service file
print_status "Installing service file (requires sudo)..."
sudo cp "$TEMP_SERVICE" "$SERVICE_FILE"
if [ $? -ne 0 ]; then
    print_error "Failed to install service file"
    rm -f "$TEMP_SERVICE"
    exit 1
fi

# Clean up
rm -f "$TEMP_SERVICE"

# Set proper permissions
sudo chmod 644 "$SERVICE_FILE"

# Reload systemd
print_status "Reloading systemd daemon..."
sudo systemctl daemon-reload

# Enable service
print_status "Enabling service for auto-start on boot..."
sudo systemctl enable "$SERVICE_NAME"
if [ $? -eq 0 ]; then
    print_success "Service enabled for auto-start"
else
    print_error "Failed to enable service"
    exit 1
fi

# Ask if user wants to start now
echo
read -p "Start the service now? (Y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Nn]$ ]]; then
    print_status "Starting service..."
    sudo systemctl start "$SERVICE_NAME"
    
    # Wait a moment and check status
    sleep 3
    
    if sudo systemctl is-active --quiet "$SERVICE_NAME"; then
        print_success "Service started successfully!"
        
        # Test API
        print_status "Testing API..."
        sleep 5
        if curl -s --max-time 5 http://localhost:5000/health > /dev/null; then
            print_success "API is responding!"
            echo "Health check: $(curl -s http://localhost:5000/health)"
        else
            print_warning "Service is running but API may still be starting up"
            print_status "Check logs with: sudo journalctl -u $SERVICE_NAME -f"
        fi
    else
        print_error "Service failed to start"
        print_status "Check logs with: sudo journalctl -u $SERVICE_NAME -f"
        exit 1
    fi
fi

echo
print_success "Installation completed!"
echo
echo "Service Management Commands:"
echo "  sudo systemctl start $SERVICE_NAME      # Start service"
echo "  sudo systemctl stop $SERVICE_NAME       # Stop service"
echo "  sudo systemctl restart $SERVICE_NAME    # Restart service"
echo "  sudo systemctl status $SERVICE_NAME     # Check status"
echo "  sudo journalctl -u $SERVICE_NAME -f     # View logs"
echo
echo "API Endpoints:"
echo "  Health: http://localhost:5000/health"
echo "  Docs:   http://localhost:5000/docs"
echo "  Test:   http://localhost:5000/test"
echo
echo "The service will automatically start on system boot."
echo "API Key: ${API_KEY:0:10}..."
