#!/usr/bin/env python3
"""
Clean environment setup for Trading Analysis API
"""

import sys
import subprocess
import os
import platform

def create_clean_environment():
    """Create a clean virtual environment"""
    print("🔧 Creating clean virtual environment...")
    
    try:
        # Remove existing venv if it exists
        if os.path.exists("venv"):
            print("📁 Removing existing virtual environment...")
            if platform.system() == "Windows":
                subprocess.run(["rmdir", "/s", "/q", "venv"], shell=True)
            else:
                subprocess.run(["rm", "-rf", "venv"])
        
        # Create new virtual environment
        subprocess.check_call([sys.executable, "-m", "venv", "venv"])
        print("✅ Virtual environment created successfully")
        
        # Get activation command
        if platform.system() == "Windows":
            activate_cmd = "venv\\Scripts\\activate"
            pip_cmd = "venv\\Scripts\\pip"
        else:
            activate_cmd = "source venv/bin/activate"
            pip_cmd = "venv/bin/pip"
        
        print(f"\n📝 To activate the environment:")
        print(f"   {activate_cmd}")
        
        # Install packages in the virtual environment
        print("\n📦 Installing packages in virtual environment...")
        subprocess.check_call([pip_cmd, "install", "--upgrade", "pip"])
        subprocess.check_call([pip_cmd, "install", "-r", "requirements.txt"])
        
        print("\n✅ Clean environment setup complete!")
        print(f"\n🚀 To run the API:")
        print(f"   {activate_cmd}")
        print("   export API_KEY='your-secret-key'")
        print("   python Trade.py")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create environment: {e}")
        return False

if __name__ == "__main__":
    create_clean_environment()