@echo off
echo 🚀 Starting Trading Analysis API
echo ================================

REM Check if virtual environment exists
if not exist "venv" (
    echo 📦 Virtual environment not found. Creating one...
    python setup_env.py
)

REM Activate virtual environment
echo 🔧 Activating virtual environment...
call venv\Scripts\activate

REM Check if API_KEY is set
if "%API_KEY%"=="" (
    echo ⚠️  API_KEY not set. Using default for testing...
    set API_KEY=test-api-key-12345
    echo 🔑 API Key set to: %API_KEY%
)

REM Start the API
echo 🌐 Starting Flask API on http://localhost:5000
echo 📖 Documentation: http://localhost:5000/docs
echo ❤️  Health check: http://localhost:5000/health
echo.
echo Press Ctrl+C to stop the server
echo.

python Trade.py