#!/usr/bin/env python3
"""
Installation script for Trading Analysis API
Handles Python version compatibility and dependency resolution
"""

import sys
import subprocess
import platform
import pkg_resources
from packaging import version

def check_python_version():
    """Check if Python version is compatible"""
    python_version = platform.python_version()
    print(f"🐍 Python version: {python_version}")
    
    if version.parse(python_version) < version.parse("3.8.0"):
        print("❌ Error: Python 3.8 or higher is required")
        print("Please upgrade Python: https://www.python.org/downloads/")
        sys.exit(1)
    elif version.parse(python_version) >= version.parse("3.12.0"):
        print("⚠️  Warning: Python 3.12+ may have compatibility issues")
        print("Recommended: Python 3.8-3.11")
    else:
        print("✅ Python version is compatible")

def install_package(package):
    """Install a package with error handling"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def install_requirements():
    """Install requirements with compatibility handling"""
    print("\n📦 Installing dependencies...")
    
    # Core dependencies with fallbacks
    dependencies = {
        "Flask": ["Flask==2.3.3", "Flask==2.2.5", "Flask>=2.0.0"],
        "TensorFlow": ["tensorflow==2.13.0", "tensorflow==2.12.0", "tensorflow>=2.10.0"],
        "NumPy": ["numpy==1.24.3", "numpy==1.23.5", "numpy>=1.21.0"],
        "Pandas": ["pandas==2.0.3", "pandas==1.5.3", "pandas>=1.4.0"],
        "Scikit-learn": ["scikit-learn==1.3.0", "scikit-learn==1.2.2", "scikit-learn>=1.1.0"],
        "Keras": ["Keras==2.13.1", "Keras==2.12.0", "Keras>=2.10.0"],
        "h5py": ["h5py==3.8.0", "h5py==3.7.0", "h5py>=3.6.0"],
        "yfinance": ["yfinance==0.2.18", "yfinance>=0.2.0"],
        "requests": ["requests==2.31.0", "requests>=2.28.0"],
        "plotly": ["plotly==5.15.0", "plotly>=5.0.0"],
        "html5lib": ["html5lib==1.1", "html5lib>=1.0"],
        "typing_extensions": ["typing_extensions==4.7.1", "typing_extensions>=4.0.0"],
        "Werkzeug": ["Werkzeug==2.3.7", "Werkzeug>=2.0.0"]
    }
    
    failed_packages = []
    
    for package_name, versions in dependencies.items():
        print(f"\n📦 Installing {package_name}...")
        installed = False
        
        for version_spec in versions:
            print(f"  Trying {version_spec}...")
            if install_package(version_spec):
                print(f"  ✅ {version_spec} installed successfully")
                installed = True
                break
            else:
                print(f"  ❌ {version_spec} failed")
        
        if not installed:
            failed_packages.append(package_name)
            print(f"  ⚠️  {package_name} installation failed with all versions")
    
    if failed_packages:
        print(f"\n⚠️  Some packages failed to install: {', '.join(failed_packages)}")
        print("You may need to install them manually or use different versions")
        return False
    else:
        print("\n✅ All dependencies installed successfully!")
        return True

def create_virtual_environment():
    """Create and activate virtual environment"""
    print("\n🔧 Setting up virtual environment...")
    try:
        subprocess.check_call([sys.executable, "-m", "venv", "venv"])
        print("✅ Virtual environment created")
        
        # Provide activation instructions
        if platform.system() == "Windows":
            activate_cmd = "venv\\Scripts\\activate"
        else:
            activate_cmd = "source venv/bin/activate"
        
        print(f"📝 To activate: {activate_cmd}")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to create virtual environment")
        return False

def main():
    """Main installation process"""
    print("🚀 Trading Analysis API Installation")
    print("=" * 40)
    
    # Check Python version
    check_python_version()
    
    # Ask about virtual environment
    use_venv = input("\n🤔 Create virtual environment? (recommended) [y/N]: ").lower().strip()
    if use_venv in ['y', 'yes']:
        if not create_virtual_environment():
            print("⚠️  Continuing without virtual environment...")
    
    # Upgrade pip first
    print("\n⬆️  Upgrading pip...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✅ pip upgraded")
    except subprocess.CalledProcessError:
        print("⚠️  pip upgrade failed, continuing...")
    
    # Install dependencies
    if install_requirements():
        print("\n🎉 Installation completed successfully!")
        print("\n🚀 To start the API:")
        print("   export API_KEY='your-secret-key'")
        print("   python Trade.py")
        print("\n📖 Documentation: http://localhost:5000/docs")
    else:
        print("\n❌ Installation completed with errors")
        print("Please check the error messages above and install failed packages manually")

if __name__ == "__main__":
    main()